import mongoose, { Document, Schema } from "mongoose";

export interface IGuest extends Document {
  name: string;
  email: string;
  mobile: string;
  work: string;
  qrCode: string;
  isScanned: boolean;
  scannedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const GuestSchema = new Schema<IGuest>(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      lowercase: true,
      trim: true,
    },
    mobile: {
      type: String,
      required: [true, "Mobile number is required"],
      trim: true,
    },
    work: {
      type: String,
      required: [true, "Work is required"],
      trim: true,
    },
    qrCode: {
      type: String,
      required: true,
      unique: true,
    },
    isScanned: {
      type: Boolean,
      default: false,
    },
    scannedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Create index for email uniqueness
GuestSchema.index({ email: 1 }, { unique: true });

export default mongoose.models.Guest ||
  mongoose.model<IGuest>("Guest", GuestSchema);
