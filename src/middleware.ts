import createMiddleware from "next-intl/middleware";

export default createMiddleware({
  // A list of all locales that are supported
  locales: ["ar", "en"],

  // Used when no locale matches - Arabic is default
  defaultLocale: "ar",

  // Always use locale prefix
  localePrefix: "always",

  // Disable automatic locale detection to prevent unwanted redirects
  localeDetection: false,

  // Alternate links for better SEO and language switching
  alternateLinks: true,
});

export const config = {
  // Match only internationalized pathnames
  matcher: [
    // Match all pathnames except for
    // - … if they start with `/api`, `/_next` or `/_vercel`
    // - … the ones containing a dot (e.g. `favicon.ico`)
    "/((?!api|_next|_vercel|.*\\..*).*)",
  ],
};
