import { notFound } from "next/navigation";
import { getRequestConfig } from "next-intl/server";

// Can be imported from a shared config
const locales = ["ar", "en"];

export default getRequestConfig(async ({ locale }) => {
  // Fallback to default locale if undefined or invalid
  const validLocale = locale && locales.includes(locale) ? locale : "ar";

  return {
    locale: validLocale,
    messages: (await import(`../messages/${validLocale}.json`)).default,
  };
});
