"use client";

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';

export default function LanguageDetector() {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Get stored language preference
    const storedLocale = localStorage.getItem('preferred-locale');
    
    // If there's a stored preference and it's different from current locale
    if (storedLocale && storedLocale !== currentLocale && (storedLocale === 'en' || storedLocale === 'ar')) {
      // Get current path segments
      const pathSegments = pathname.split('/').filter(Boolean);
      
      // Remove current locale from path
      if (pathSegments.length > 0 && (pathSegments[0] === 'en' || pathSegments[0] === 'ar')) {
        pathSegments.shift();
      }
      
      // Construct new path with preferred locale
      const newPath = `/${storedLocale}${pathSegments.length > 0 ? '/' + pathSegments.join('/') : ''}`;
      
      // Navigate to preferred language version
      router.replace(newPath);
    }
  }, [currentLocale, pathname, router]);

  // This component doesn't render anything
  return null;
}
