"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Globe, Menu, X } from "lucide-react";

export default function Navigation() {
  const t = useTranslations("navigation");
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Extract locale from pathname - much more reliable
  const currentLocale = pathname.startsWith("/en") ? "en" : "ar";

  // Debug locale detection

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  // Handle escape key to close mobile menu
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isMobileMenuOpen]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link
              href={`/${currentLocale}`}
              className="text-xl font-bold text-gray-900"
              onClick={closeMobileMenu}
            >
              <img src="/logo.jpeg" alt="Logo" className="h-16 mr-2" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div
            className={`hidden md:flex items-center ${
              currentLocale === "ar" ? "space-x-reverse space-x-4" : "space-x-4"
            }`}
          >
            <Link href={`/${currentLocale}/guest`}>
              <Button variant="ghost">{t("guest")}</Button>
            </Link>
            <Link href={`/${currentLocale}/company`}>
              <Button variant="ghost">{t("company")}</Button>
            </Link>

            {/* English Language Button - Show when on Arabic page */}
            {currentLocale === "ar" && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  // Store language preference
                  if (typeof window !== "undefined") {
                    localStorage.setItem("preferred-locale", "en");
                  }
                  // Get current path segments without locale
                  const pathSegments = pathname.split("/").filter(Boolean);
                  if (
                    pathSegments.length > 0 &&
                    (pathSegments[0] === "ar" || pathSegments[0] === "en")
                  ) {
                    pathSegments.shift(); // Remove current locale
                  }
                  // Construct new path with English locale
                  const newPath = `/en${
                    pathSegments.length > 0 ? "/" + pathSegments.join("/") : ""
                  }`;
                  window.location.assign(newPath);
                }}
                className="flex items-center space-x-reverse space-x-2"
              >
                <Globe className="h-4 w-4" />
                <span>English</span>
              </Button>
            )}

            {/* Arabic Language Button - Show when on English page */}
            {currentLocale === "en" && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  // Store language preference
                  if (typeof window !== "undefined") {
                    localStorage.setItem("preferred-locale", "ar");
                  }
                  // Get current path segments without locale
                  const pathSegments = pathname.split("/").filter(Boolean);
                  if (
                    pathSegments.length > 0 &&
                    (pathSegments[0] === "ar" || pathSegments[0] === "en")
                  ) {
                    pathSegments.shift(); // Remove current locale
                  }
                  // Construct new path with Arabic locale
                  const newPath = `/ar${
                    pathSegments.length > 0 ? "/" + pathSegments.join("/") : ""
                  }`;
                  window.location.assign(newPath);
                }}
                className="flex items-center space-x-2"
              >
                <Globe className="h-4 w-4" />
                <span>العربية</span>
              </Button>
            )}

            {/* Debug: Show if no button is rendered */}
            {currentLocale !== "ar" && currentLocale !== "en" && (
              <div style={{ color: "red" }}>
                DEBUG: Unknown locale "{currentLocale}"
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMobileMenu}
              className="p-2"
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden mobile-menu">
            <div
              className={`px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t shadow-lg ${
                currentLocale === "ar" ? "text-right" : "text-left"
              }`}
            >
              <Link href={`/${currentLocale}/guest`} onClick={closeMobileMenu}>
                <Button
                  variant="ghost"
                  className={`w-full ${
                    currentLocale === "ar"
                      ? "justify-end text-right"
                      : "justify-start text-left"
                  }`}
                >
                  {t("guest")}
                </Button>
              </Link>

              <Link
                href={`/${currentLocale}/company`}
                onClick={closeMobileMenu}
              >
                <Button
                  variant="ghost"
                  className={`w-full ${
                    currentLocale === "ar"
                      ? "justify-end text-right"
                      : "justify-start text-left"
                  }`}
                >
                  {t("company")}
                </Button>
              </Link>

              <div className="pt-2 border-t border-gray-200">
                {/* English Language Button - Show when on Arabic page */}
                {currentLocale === "ar" && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      // Store language preference
                      if (typeof window !== "undefined") {
                        localStorage.setItem("preferred-locale", "en");
                      }
                      // Get current path segments without locale
                      const pathSegments = pathname.split("/").filter(Boolean);
                      if (
                        pathSegments.length > 0 &&
                        (pathSegments[0] === "ar" || pathSegments[0] === "en")
                      ) {
                        pathSegments.shift(); // Remove current locale
                      }
                      // Construct new path with English locale
                      const newPath = `/en${
                        pathSegments.length > 0
                          ? "/" + pathSegments.join("/")
                          : ""
                      }`;
                      window.location.assign(newPath);
                      closeMobileMenu();
                    }}
                    className="w-full flex items-center justify-center space-x-reverse space-x-2"
                  >
                    <Globe className="h-4 w-4" />
                    <span>English</span>
                  </Button>
                )}

                {/* Arabic Language Button - Show when on English page */}
                {currentLocale === "en" && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      // Store language preference
                      if (typeof window !== "undefined") {
                        localStorage.setItem("preferred-locale", "ar");
                      }
                      // Get current path segments without locale
                      const pathSegments = pathname.split("/").filter(Boolean);
                      if (
                        pathSegments.length > 0 &&
                        (pathSegments[0] === "ar" || pathSegments[0] === "en")
                      ) {
                        pathSegments.shift(); // Remove current locale
                      }
                      // Construct new path with Arabic locale
                      const newPath = `/ar${
                        pathSegments.length > 0
                          ? "/" + pathSegments.join("/")
                          : ""
                      }`;
                      window.location.assign(newPath);
                      closeMobileMenu();
                    }}
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <Globe className="h-4 w-4" />
                    <span>العربية</span>
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
