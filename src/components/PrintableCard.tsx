"use client";

import { forwardRef } from "react";
import { useLocale, useTranslations } from "next-intl";

interface PrintableCardProps {
  type: "guest" | "company";
  data: any;
  qrCodeDataURL: string;
}

const PrintableCard = forwardRef<HTMLDivElement, PrintableCardProps>(
  ({ type, data, qrCodeDataURL }, ref) => {
    const locale = useLocale();
    const t = useTranslations();

    return (
      <div
        ref={ref}
        className="bg-white p-8 rounded-lg shadow-lg max-w-4xl mx-auto"
        style={{
          minHeight: "400px",
          fontFamily:
            locale === "ar"
              ? "Noto Sans Arabic, sans-serif"
              : "system-ui, sans-serif",
        }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-lg mb-4">
            <h1 className="text-2xl font-bold mb-2">
              {type === "guest" ? t("card.guestCard") : t("card.companyCard")}
            </h1>
            <p className="text-blue-100">
              {t("card.exhibitionName")} {t("card.exhibitionYear")}
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {/* Information Section */}
          <div className="md:col-span-2 space-y-6">
            {type === "guest" ? (
              <>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "الاسم الكامل" : "Full Name"}
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {data.name}
                    </p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "المهنة" : "Profession"}
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {data.work}
                    </p>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "البريد الإلكتروني" : "Email"}
                    </label>
                    <p className="text-gray-900">{data.email}</p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "رقم الهاتف" : "Mobile Number"}
                    </label>
                    <p className="text-gray-900">{data.mobile}</p>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                  <label className="text-sm font-medium text-blue-700 block mb-1">
                    {locale === "ar" ? "اسم الشركة" : "Company Name"}
                  </label>
                  <p className="text-xl font-bold text-blue-900">
                    {data.workName}
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "المسؤول" : "Responsible Person"}
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {data.responsible}
                    </p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "المنصب" : "Position"}
                    </label>
                    <p className="text-lg font-semibold text-gray-900">
                      {data.position}
                    </p>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "البريد الإلكتروني" : "Email"}
                    </label>
                    <p className="text-gray-900">{data.email}</p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <label className="text-sm font-medium text-gray-500 block mb-1">
                      {locale === "ar" ? "واتساب" : "WhatsApp"}
                    </label>
                    <p className="text-gray-900">{data.whatsapp}</p>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <label className="text-sm font-medium text-gray-500 block mb-1">
                    {locale === "ar" ? "مجال العمل" : "Work Field"}
                  </label>
                  <p className="text-gray-900">{data.workField}</p>
                </div>
              </>
            )}
          </div>

          {/* QR Code Section */}
          <div className="text-center">
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-lg border-2 border-gray-200">
              <img
                src={qrCodeDataURL}
                alt="QR Code"
                className="mx-auto mb-4"
                style={{ width: "180px", height: "180px" }}
              />
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-1">{t("card.showAtEntry")}</p>
                <p className="text-xs">{t("card.showAtEntry")}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex justify-between items-center text-sm text-gray-500">
            <div>
              <p>
                {t("card.registrationDate")}:{" "}
                {new Date(data.createdAt).toLocaleDateString(
                  locale === "ar" ? "ar-SA" : "en-US"
                )}
              </p>
            </div>
            <div className="text-right">
              <p className="font-medium">{t("card.exhibitionName")}</p>
              <p>{t("card.exhibitionYear")}</p>
            </div>
          </div>
        </div>

        {/* Print-only styles */}
        <style jsx>{`
          @media print {
            .bg-gradient-to-r {
              background: #4f46e5 !important;
              color: white !important;
            }
            .bg-gradient-to-br {
              background: #f9fafb !important;
            }
            .shadow-lg {
              box-shadow: none !important;
            }
          }
        `}</style>
      </div>
    );
  }
);

PrintableCard.displayName = "PrintableCard";

export default PrintableCard;
