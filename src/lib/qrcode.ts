import QRCode from "qrcode";
import { v4 as uuidv4 } from "uuid";

export const generateQRCode = async (
  data: any,
  type: "guest" | "company"
): Promise<string> => {
  const qrData = {
    id: uuidv4(),
    type,
    data,
    timestamp: new Date().toISOString(),
  };

  try {
    const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(qrData), {
      errorCorrectionLevel: "M",
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    return qrCodeDataURL;
  } catch (error) {
    console.error("Error generating QR code:", error);
    throw new Error("Failed to generate QR code");
  }
};

export const generateUniqueQRId = (): string => {
  return uuidv4();
};

export const validateQRCode = (
  qrCodeData: string
): { isValid: boolean; data?: any; type?: string } => {
  try {
    const parsed = JSON.parse(qrCodeData);

    if (!parsed.id || !parsed.type || !parsed.data || !parsed.timestamp) {
      return { isValid: false };
    }

    if (!["guest", "company"].includes(parsed.type)) {
      return { isValid: false };
    }

    return {
      isValid: true,
      data: parsed.data,
      type: parsed.type,
    };
  } catch (error) {
    return { isValid: false };
  }
};
