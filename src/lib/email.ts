import nodemailer from "nodemailer";

const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT || "587"),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

export const sendGuestConfirmationEmail = async (
  email: string,
  name: string,
  qrCodeDataURL: string,
  locale: string = "en"
) => {
  // Convert data URL to buffer for attachment
  const qrCodeBuffer = Buffer.from(qrCodeDataURL.split(",")[1], "base64");
  const isArabic = locale === "ar";

  const subject = isArabic
    ? "تأكيد التسجيل - بطاقة الدخول الخاصة بك"
    : "Registration Confirmation - Your Entry Card";

  const htmlContent = `
    <!DOCTYPE html>
    <html dir="${isArabic ? "rtl" : "ltr"}" lang="${locale}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body {
          font-family: ${isArabic ? "Arial, sans-serif" : "Arial, sans-serif"};
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          direction: ${isArabic ? "rtl" : "ltr"};
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 30px;
          text-align: center;
          border-radius: 10px 10px 0 0;
        }
        .content {
          background: #f9f9f9;
          padding: 30px;
          border-radius: 0 0 10px 10px;
        }
        .qr-container {
          text-align: center;
          margin: 30px 0;
          padding: 20px;
          background: white;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-code {
          max-width: 200px;
          height: auto;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding: 20px;
          background: #333;
          color: white;
          border-radius: 10px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${isArabic ? "مرحباً " + name : "Welcome " + name}</h1>
        <p>${
          isArabic
            ? "تم تسجيلك بنجاح في المعرض"
            : "You have successfully registered for the exhibition"
        }</p>
      </div>
      
      <div class="content">
        <h2>${isArabic ? "بطاقة الدخول الخاصة بك" : "Your Entry Card"}</h2>
        <p>${
          isArabic
            ? "يرجى الاحتفاظ بهذه البطاقة وإظهارها عند الدخول إلى المعرض."
            : "Please keep this card and show it when entering the exhibition."
        }</p>
        
        <div class="qr-container">
          <img src="cid:qrcode" alt="QR Code" class="qr-code" />
          <p><strong>${
            isArabic ? "رمز الاستجابة السريعة الخاص بك" : "Your QR Code"
          }</strong></p>
        </div>
        
        <p>${
          isArabic ? "معلوماتك المسجلة:" : "Your registered information:"
        }</p>
        <ul>
          <li><strong>${isArabic ? "الاسم:" : "Name:"}</strong> ${name}</li>
          <li><strong>${
            isArabic ? "البريد الإلكتروني:" : "Email:"
          }</strong> ${email}</li>
        </ul>
      </div>
      
      <div class="footer">
        <p>${
          isArabic
            ? "شكراً لتسجيلك معنا!"
            : "Thank you for registering with us!"
        }</p>
      </div>
    </body>
    </html>
  `;

  try {
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject,
      html: htmlContent,
      attachments: [
        {
          filename: "qr-code.png",
          content: qrCodeBuffer,
          cid: "qrcode", // Content-ID for embedding in HTML
        },
      ],
    });
  } catch (error) {
    console.error("Error sending guest confirmation email:", error);
    throw new Error("Failed to send confirmation email");
  }
};

export const sendCompanyConfirmationEmail = async (
  email: string,
  responsible: string,
  workName: string,
  qrCodeDataURL: string,
  locale: string = "en"
) => {
  // Convert data URL to buffer for attachment
  const qrCodeBuffer = Buffer.from(qrCodeDataURL.split(",")[1], "base64");
  const isArabic = locale === "ar";

  const subject = isArabic
    ? "تأكيد تسجيل الشركة - بطاقة الدخول الخاصة بكم"
    : "Company Registration Confirmation - Your Entry Card";

  const htmlContent = `
    <!DOCTYPE html>
    <html dir="${isArabic ? "rtl" : "ltr"}" lang="${locale}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body {
          font-family: ${isArabic ? "Arial, sans-serif" : "Arial, sans-serif"};
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          direction: ${isArabic ? "rtl" : "ltr"};
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 30px;
          text-align: center;
          border-radius: 10px 10px 0 0;
        }
        .content {
          background: #f9f9f9;
          padding: 30px;
          border-radius: 0 0 10px 10px;
        }
        .qr-container {
          text-align: center;
          margin: 30px 0;
          padding: 20px;
          background: white;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-code {
          max-width: 200px;
          height: auto;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding: 20px;
          background: #333;
          color: white;
          border-radius: 10px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${
          isArabic ? "مرحباً بشركة " + workName : "Welcome " + workName
        }</h1>
        <p>${
          isArabic
            ? "تم تسجيل شركتكم بنجاح في المعرض"
            : "Your company has successfully registered for the exhibition"
        }</p>
      </div>
      
      <div class="content">
        <h2>${
          isArabic ? "بطاقة الدخول الخاصة بالشركة" : "Your Company Entry Card"
        }</h2>
        <p>${
          isArabic
            ? "يرجى الاحتفاظ بهذه البطاقة وإظهارها عند الدخول إلى المعرض."
            : "Please keep this card and show it when entering the exhibition."
        }</p>
        
        <div class="qr-container">
          <img src="cid:qrcode" alt="QR Code" class="qr-code" />
          <p><strong>${
            isArabic
              ? "رمز الاستجابة السريعة الخاص بالشركة"
              : "Your Company QR Code"
          }</strong></p>
        </div>
        
        <p>${
          isArabic
            ? "معلومات الشركة المسجلة:"
            : "Your registered company information:"
        }</p>
        <ul>
          <li><strong>${
            isArabic ? "اسم الشركة:" : "Company Name:"
          }</strong> ${workName}</li>
          <li><strong>${
            isArabic ? "المسؤول:" : "Responsible Person:"
          }</strong> ${responsible}</li>
          <li><strong>${
            isArabic ? "البريد الإلكتروني:" : "Email:"
          }</strong> ${email}</li>
        </ul>
      </div>
      
      <div class="footer">
        <p>${
          isArabic
            ? "شكراً لتسجيل شركتكم معنا!"
            : "Thank you for registering your company with us!"
        }</p>
      </div>
    </body>
    </html>
  `;

  try {
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject,
      html: htmlContent,
      attachments: [
        {
          filename: "company-qr-code.png",
          content: qrCodeBuffer,
          cid: "qrcode", // Content-ID for embedding in HTML
        },
      ],
    });
  } catch (error) {
    console.error("Error sending company confirmation email:", error);
    throw new Error("Failed to send confirmation email");
  }
};
