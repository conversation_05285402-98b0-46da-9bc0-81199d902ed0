"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Home,
  ArrowLeft,
  Users,
  Building2,
  RefreshCw,
  ExternalLink,
} from "lucide-react";

export default function NotFound() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [isArabic, setIsArabic] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Check if user prefers Arabic based on URL path or browser language
    const currentPath = window.location.pathname;
    const isArabicFromUrl = currentPath.startsWith("/ar");
    const browserLang = navigator.language;
    const isArabicBrowser = browserLang.startsWith("ar");

    // Prefer URL language over browser language
    setIsArabic(
      isArabicFromUrl || (!currentPath.startsWith("/en") && isArabicBrowser)
    );
  }, []);

  const handleLanguageChange = (newIsArabic: boolean) => {
    setIsArabic(newIsArabic);
    // Navigate to the home page with the new locale
    const newLocale = newIsArabic ? "ar" : "en";
    router.push(`/${newLocale}`);
  };

  const getQuickLinks = () => {
    const locale = isArabic ? "ar" : "en";
    return [
      {
        href: `/${locale}`,
        icon: Home,
        title: isArabic ? "الرئيسية" : "Home",
        description: isArabic ? "الذهاب للصفحة الرئيسية" : "Go to homepage",
        color: "bg-blue-600 hover:bg-blue-700",
      },
      {
        href: `/${locale}/guest`,
        icon: Users,
        title: isArabic ? "تسجيل ضيف" : "Guest Registration",
        description: isArabic ? "التسجيل كضيف" : "Register as a guest",
        color: "bg-green-600 hover:bg-green-700",
      },
      {
        href: `/${locale}/company`,
        icon: Building2,
        title: isArabic ? "تسجيل شركة" : "Company Registration",
        description: isArabic ? "تسجيل شركتك" : "Register your company",
        color: "bg-purple-600 hover:bg-purple-700",
      },
    ];
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl w-full text-center">
          {/* 404 Animation */}
          <div className="mb-8">
            <div className="inline-block animate-bounce">
              <h1 className="text-8xl sm:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 mb-4">
                404
              </h1>
            </div>
            <div className="animate-pulse">
              <div className="w-24 h-1 bg-gradient-to-r from-purple-600 to-blue-600 mx-auto rounded-full"></div>
            </div>
          </div>

          {/* Error Message */}
          <div className="mb-12 space-y-4" dir={isArabic ? "rtl" : "ltr"}>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              {isArabic ? "عذراً! الصفحة غير موجودة" : "Oops! Page Not Found"}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
              {isArabic
                ? "يبدو أن الصفحة التي تبحث عنها قد ضاعت في الفضاء الرقمي. لا تقلق، حتى أفضل المستكشفين يأخذون منعطفاً خاطئاً أحياناً!"
                : "The page you're looking for seems to have wandered off into the digital void. Don't worry, even the best explorers sometimes take a wrong turn!"}
            </p>
          </div>

          {/* Quick Links Grid */}
          <div
            className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-12"
            dir={isArabic ? "rtl" : "ltr"}
          >
            {getQuickLinks().map((link, index) => (
              <Link
                key={link.href}
                href={link.href}
                className="group relative bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-white/20"
                style={{
                  animationDelay: `${index * 100}ms`,
                  animation: "fadeInUp 0.6s ease-out forwards",
                }}
              >
                <div
                  className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${link.color} text-white mb-4 group-hover:scale-110 transition-transform duration-200`}
                >
                  <link.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {link.title}
                </h3>
                <p className="text-sm text-gray-600">{link.description}</p>
                <ExternalLink className="absolute top-4 right-4 h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
              </Link>
            ))}
          </div>

          {/* Action Buttons */}
          <div
            className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8"
            dir={isArabic ? "rtl" : "ltr"}
          >
            <button
              onClick={() => router.back()}
              className="inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              <ArrowLeft className={`h-5 w-5 ${isArabic ? "ml-2" : "mr-2"}`} />
              {isArabic ? "العودة" : "Go Back"}
            </button>

            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              <RefreshCw className={`h-5 w-5 ${isArabic ? "ml-2" : "mr-2"}`} />
              {isArabic ? "تحديث" : "Refresh"}
            </button>
          </div>

          {/* Language Toggle */}
          <div className="flex items-center justify-center gap-4">
            <button
              onClick={() => handleLanguageChange(false)}
              className={`inline-flex items-center px-4 py-2 backdrop-blur-sm rounded-lg transition-all duration-200 shadow-md border border-white/20 ${
                !isArabic
                  ? "bg-blue-600 text-white"
                  : "bg-white/80 text-gray-700 hover:bg-white"
              }`}
            >
              🇺🇸 English
            </button>
            <button
              onClick={() => handleLanguageChange(true)}
              className={`inline-flex items-center px-4 py-2 backdrop-blur-sm rounded-lg transition-all duration-200 shadow-md border border-white/20 ${
                isArabic
                  ? "bg-blue-600 text-white"
                  : "bg-white/80 text-gray-700 hover:bg-white"
              }`}
            >
              🇸🇦 العربية
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
