"use client";

import { useState, useEffect, useRef } from "react";
import { useTranslations, useLocale } from "next-intl";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Download, Printer, Share2 } from "lucide-react";

interface GuestData {
  _id: string;
  name: string;
  email: string;
  mobile: string;
  work: string;
  qrCode: string;
  createdAt: string;
}

export default function GuestCard({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  const [id, setId] = useState<string>("");
  const t = useTranslations();
  const locale = useLocale();

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setId(resolvedParams.id);
    };
    getParams();
  }, [params]);
  const cardRef = useRef<HTMLDivElement>(null);

  const [guest, setGuest] = useState<GuestData | null>(null);
  const [qrCodeDataURL, setQrCodeDataURL] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (id) {
      fetchGuestData();
    }
  }, [id]);

  const fetchGuestData = async () => {
    try {
      const response = await fetch(`/api/guest/${id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch guest data");
      }

      setGuest(data.guest);
      setQrCodeDataURL(data.qrCodeDataURL);
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const downloadQROnly = () => {
    if (!qrCodeDataURL || !guest) return;

    const link = document.createElement("a");
    link.download = `guest-qr-${guest.name.replace(/\s+/g, "-")}.png`;
    link.href = qrCodeDataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const printCard = () => {
    window.print();
  };

  const shareCard = async () => {
    if (navigator.share && guest) {
      try {
        await navigator.share({
          title: `${guest.name} - Exhibition Card`,
          text: `Exhibition entry card for ${guest.name}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert(t("messages.linkCopied"));
    }
  };

  if (loading) {
    return (
      <div
        className="min-h-screen"
        style={{
          background: "linear-gradient(135deg, #3C2F6A 0%, #222147 100%)",
        }}
      >
        <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto"></div>
            <p className="mt-4 text-white">{t("common.loading")}</p>
          </div>
        </main>
      </div>
    );
  }

  if (error || !guest) {
    return (
      <div
        className="min-h-screen"
        style={{
          background: "linear-gradient(135deg, #3C2F6A 0%, #222147 100%)",
        }}
      >
        <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <Alert variant="destructive">
            <AlertDescription>
              {error || t("messages.guestNotFound")}
            </AlertDescription>
          </Alert>
        </main>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen"
      style={{
        background: "linear-gradient(135deg, #3C2F6A 0%, #222147 100%)",
      }}
    >
      <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-6">
          {/* Card Preview */}
          <div
            ref={cardRef}
            className="print-area bg-white p-8 rounded-lg shadow-lg"
          >
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {t("card.guestCard")}
              </h1>
              <div className="w-20 h-1 bg-blue-600 mx-auto"></div>
            </div>

            <div className="grid md:grid-cols-2 gap-6 items-center">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("common.name")}
                  </label>
                  <p className="text-lg font-semibold text-gray-900">
                    {guest.name}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("common.email")}
                  </label>
                  <p className="text-gray-900">{guest.email}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("common.mobile")}
                  </label>
                  <p className="text-gray-900">{guest.mobile}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    {t("common.work")}
                  </label>
                  <p className="text-gray-900">{guest.work}</p>
                </div>
              </div>

              <div className="text-center">
                {qrCodeDataURL && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <img
                      src={qrCodeDataURL}
                      alt="QR Code"
                      className="mx-auto mb-2"
                      style={{ width: "200px", height: "200px" }}
                    />
                    <p className="text-sm text-gray-600">
                      {t("card.showAtEntry")}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 text-center text-sm text-gray-500">
              {t("card.registrationDate")}:{" "}
              {new Date(guest.createdAt).toLocaleDateString(
                locale === "ar" ? "ar-SA" : "en-US"
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <Card className="no-print">
            <CardHeader>
              <CardTitle>{t("card.title")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  onClick={downloadQROnly}
                  variant="outline"
                  className="flex-1"
                >
                  <Download className="h-4 w-4 mr-2" />
                  {t("card.downloadQR")}
                </Button>
                <Button
                  onClick={printCard}
                  variant="outline"
                  className="flex-1"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  {t("card.print")}
                </Button>
                <Button
                  onClick={shareCard}
                  variant="secondary"
                  className="flex-1"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  {t("card.share")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }

          .print-area,
          .print-area * {
            visibility: visible;
          }

          .print-area {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }

          .no-print {
            display: none !important;
          }

          .bg-gradient-to-br {
            background: #f3f4f6 !important;
          }
        }
      `}</style>
    </div>
  );
}
