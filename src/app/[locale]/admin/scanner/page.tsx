"use client";

import { useState, useEffect, useRef } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import {
  Camera,
  CheckCircle,
  XCircle,
  User,
  Building2,
  Scan,
  Upload,
  AlertTriangle,
} from "lucide-react";
import { BrowserMultiFormatReader } from "@zxing/library";

interface ScanResult {
  success: boolean;
  type?: "guest" | "company";
  data?: {
    name?: string;
    responsible?: string;
    email: string;
    [key: string]: unknown;
  };
  message: string;
  alreadyScanned?: boolean;
}

type CameraPermissionState = "prompt" | "granted" | "denied" | "checking";

export default function QRScanner() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [scanning, setScanning] = useState(false);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [error, setError] = useState("");
  const [codeReader, setCodeReader] = useState<BrowserMultiFormatReader | null>(
    null
  );
  const [cameraPermission, setCameraPermission] =
    useState<CameraPermissionState>("prompt");
  const [isCheckingPermission, setIsCheckingPermission] = useState(false);

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("adminToken");
    if (!token) {
      router.push(`/${locale}/admin`);
      return;
    }

    // Initialize code reader
    const reader = new BrowserMultiFormatReader();
    setCodeReader(reader);

    // Check initial camera permission status
    checkCameraPermission();

    return () => {
      if (reader) {
        reader.reset();
      }
    };
  }, [locale, router]);

  const checkCameraPermission = async () => {
    // Simple check for modern getUserMedia support
    if (!navigator.mediaDevices?.getUserMedia) {
      setError("Camera access is not supported in this browser.");
      return;
    }

    // Check if we're in a secure context
    if (
      typeof window !== "undefined" &&
      window.location.protocol !== "https:" &&
      window.location.hostname !== "localhost"
    ) {
      setError(
        "Camera access requires HTTPS. Please access this page over HTTPS."
      );
      return;
    }

    if (!navigator.permissions) {
      setCameraPermission("prompt");
      return;
    }

    try {
      const permission = await navigator.permissions.query({
        name: "camera" as PermissionName,
      });
      setCameraPermission(permission.state as CameraPermissionState);

      // Listen for permission changes
      permission.onchange = () => {
        setCameraPermission(permission.state as CameraPermissionState);
        // If permission was granted, clear any previous errors
        if (permission.state === "granted") {
          setError("");
        }
      };
    } catch {
      // Fallback for browsers that don't support permissions API
      setCameraPermission("prompt");
    }
  };

  const requestCameraPermission = async () => {
    setIsCheckingPermission(true);
    setError("");

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" },
      });

      // Permission granted, stop the stream immediately
      stream.getTracks().forEach((track) => track.stop());
      setCameraPermission("granted");

      // Now start scanning immediately
      await startScanningWithPermission();
    } catch (error) {
      const err = error as Error & { name: string };
      if (
        err.name === "NotAllowedError" ||
        err.name === "PermissionDeniedError"
      ) {
        setCameraPermission("denied");
        setError(t("messages.cameraPermissionDenied"));
      } else if (
        err.name === "NotFoundError" ||
        err.name === "DevicesNotFoundError"
      ) {
        setError(t("messages.noCameraFound"));
      } else {
        setError(t("messages.failedToAccessCamera") + ": " + err.message);
      }
    } finally {
      setIsCheckingPermission(false);
    }
  };

  const startScanningWithPermission = async () => {
    if (!codeReader || !videoRef.current) return;

    try {
      setScanning(true);
      setError("");
      setScanResult(null);

      // Stop any existing stream first
      if (videoRef.current.srcObject) {
        const existingStream = videoRef.current.srcObject as MediaStream;
        existingStream.getTracks().forEach((track) => track.stop());
        videoRef.current.srcObject = null;
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment",
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;

        // Wait for the video metadata to load
        await new Promise<void>((resolve, reject) => {
          if (!videoRef.current) {
            reject(new Error("Video element not available"));
            return;
          }

          const onLoadedMetadata = () => {
            videoRef.current?.removeEventListener(
              "loadedmetadata",
              onLoadedMetadata
            );
            resolve();
          };

          const onError = () => {
            videoRef.current?.removeEventListener("error", onError);
            reject(new Error("Video failed to load"));
          };

          videoRef.current.addEventListener("loadedmetadata", onLoadedMetadata);
          videoRef.current.addEventListener("error", onError);

          // Force load if not already loading
          if (videoRef.current.readyState === 0) {
            videoRef.current.load();
          }
        });

        // Now play the video
        await videoRef.current.play();

        // Start QR code scanning using ZXing
        codeReader.decodeFromVideoDevice(
          null,
          videoRef.current,
          (result, err) => {
            if (result) {
              handleScanResult(result.getText());
              stopScanning();
            }
            // Handle scanning errors if needed
            if (err && err.name !== "NotFoundException") {
              console.warn("QR scanning error:", err);
            }
          }
        );
      }
    } catch (error) {
      const err = error as Error & { name: string };
      if (
        err.name === "NotAllowedError" ||
        err.name === "PermissionDeniedError"
      ) {
        setCameraPermission("denied");
        setError(t("messages.cameraPermissionDenied"));
      } else if (
        err.name === "NotFoundError" ||
        err.name === "DevicesNotFoundError"
      ) {
        setError(t("messages.noCameraFound"));
      } else {
        setError(t("messages.failedToAccessCamera") + ": " + err.message);
      }
      setScanning(false);
    }
  };

  const startScanning = async () => {
    if (!codeReader || !videoRef.current) return;

    // Check permission first
    if (cameraPermission === "denied") {
      setError(t("messages.cameraPermissionDenied"));
      return;
    }

    if (cameraPermission === "prompt") {
      await requestCameraPermission();
      return;
    }

    // If permission is already granted, start scanning directly
    await startScanningWithPermission();
  };

  const stopScanning = () => {
    if (codeReader) {
      codeReader.reset();
    }

    if (videoRef.current) {
      // Pause video first
      videoRef.current.pause();

      // Stop all tracks
      if (videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach((track) => track.stop());
        videoRef.current.srcObject = null;
      }

      // Clear video source
      videoRef.current.load();
    }

    setScanning(false);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !codeReader) return;

    codeReader
      .decodeFromImageUrl(URL.createObjectURL(file))
      .then((result) => {
        handleScanResult(result.getText());
      })
      .catch(() => {
        setError(t("messages.couldNotReadQR"));
      });
  };

  const handleScanResult = async (qrData: string) => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch("/api/admin/scan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ qrData }),
      });

      const data = await response.json();
      setScanResult(data);

      if (!response.ok && response.status === 401) {
        localStorage.removeItem("adminToken");
        router.push(`/${locale}/admin`);
      }
    } catch {
      setError(t("messages.failedToProcessQR"));
    }
  };

  const resetScanner = () => {
    setScanResult(null);
    setError("");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      <Navigation />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t("admin.scanner.title")}
          </h1>
          <p className="text-gray-600">{t("messages.scanDescription")}</p>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Browser Compatibility Info */}

        {scanResult && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center mb-4">
                {scanResult.success ? (
                  <CheckCircle className="h-16 w-16 text-green-500" />
                ) : (
                  <XCircle className="h-16 w-16 text-red-500" />
                )}
              </div>

              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">
                  {scanResult.success
                    ? t("admin.scanner.success")
                    : t("admin.scanner.invalid")}
                </h3>
                <p className="text-gray-600 mb-4">{scanResult.message}</p>

                {scanResult.success && scanResult.data && (
                  <div className="bg-gray-50 p-4 rounded-lg mb-4">
                    <div className="flex items-center justify-center mb-2">
                      {scanResult.type === "guest" ? (
                        <User className="h-5 w-5 mr-2 text-blue-600" />
                      ) : (
                        <Building2 className="h-5 w-5 mr-2 text-green-600" />
                      )}
                      <Badge
                        variant={
                          scanResult.type === "guest" ? "default" : "secondary"
                        }
                      >
                        {scanResult.type === "guest"
                          ? t("admin.tables.guests")
                          : t("admin.tables.companies")}
                      </Badge>
                    </div>

                    <div className="text-sm text-gray-700">
                      <p>
                        <strong>{t("common.name")}:</strong>{" "}
                        {scanResult.data.name || scanResult.data.responsible}
                      </p>
                      <p>
                        <strong>{t("common.email")}:</strong>{" "}
                        {scanResult.data.email}
                      </p>
                      {scanResult.alreadyScanned && (
                        <p className="text-orange-600 mt-2">
                          {t("messages.alreadyScanned")}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <Button onClick={resetScanner} variant="outline">
                  {t("messages.scanAnother")}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {!scanResult && (
          <div className="grid md:grid-cols-2 gap-6">
            {/* Camera Scanner */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Camera className="h-5 w-5 mr-2" />
                  {t("messages.cameraScanner")}
                </CardTitle>
                <CardDescription>
                  {t("messages.useCameraToScan")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Video element - always present but hidden when not scanning */}
                <video
                  ref={videoRef}
                  className={`w-full h-64 rounded-lg object-cover ${
                    scanning ? "block" : "hidden"
                  }`}
                  autoPlay
                  playsInline
                  muted
                />

                {scanning ? (
                  <div className="space-y-4 mt-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600 mb-4">
                        {t("admin.scanner.scanning")}
                      </p>
                      <Button onClick={stopScanning} variant="outline">
                        {t("messages.stop")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                      {cameraPermission === "denied" ? (
                        <div className="flex flex-col items-center">
                          <XCircle className="h-16 w-16 text-red-400 mb-2" />
                          <p className="text-sm text-red-600 text-center">
                            {t("messages.cameraPermissionDenied")}
                          </p>
                        </div>
                      ) : cameraPermission === "checking" ||
                        isCheckingPermission ? (
                        <div className="flex flex-col items-center">
                          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600"></div>
                          <p className="text-sm text-gray-600 mt-2">
                            {t("messages.checkingPermissions")}
                          </p>
                        </div>
                      ) : (
                        <Scan className="h-16 w-16 text-gray-400" />
                      )}
                    </div>

                    {cameraPermission === "denied" ? (
                      <div className="space-y-2">
                        <Alert variant="destructive">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            {t("messages.cameraPermissionDeniedHelp")}
                          </AlertDescription>
                        </Alert>
                        <Button
                          onClick={checkCameraPermission}
                          variant="outline"
                          className="w-full"
                        >
                          {t("messages.recheckPermissions")}
                        </Button>
                      </div>
                    ) : (
                      <Button
                        onClick={startScanning}
                        className="w-full"
                        disabled={isCheckingPermission}
                      >
                        <Camera className="h-4 w-4 mr-2" />
                        {cameraPermission === "prompt"
                          ? t("messages.allowCameraAccess")
                          : t("messages.startScanning")}
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="h-5 w-5 mr-2" />
                  {t("messages.uploadImage")}
                </CardTitle>
                <CardDescription>
                  {t("messages.uploadImageDescription")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center mb-4 border-2 border-dashed border-gray-300">
                    <Upload className="h-16 w-16 text-gray-400" />
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {t("messages.chooseFile")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  );
}
