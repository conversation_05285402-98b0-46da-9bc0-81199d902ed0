"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Navigation from "@/components/Navigation";
import {
  ArrowLeft,
  Eye,
  Download,
  CheckCircle,
  XCircle,
  Search,
  Filter,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";

interface Company {
  _id: string;
  responsible: string;
  workName: string;
  position: string;
  whatsapp: string;
  workField: string;
  email: string;
  howKnew: string;
  isScanned: boolean;
  scannedAt?: string;
  createdAt: string;
}

export default function CompaniesManagement() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();

  const [companies, setCompanies] = useState<Company[]>([]);
  const [filteredCompanies, setFilteredCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("adminToken");
    if (!token) {
      router.push(`/${locale}/admin`);
      return;
    }

    fetchCompanies();
  }, [locale, router]);

  useEffect(() => {
    filterCompanies();
  }, [companies, searchTerm, statusFilter]);

  const fetchCompanies = async () => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch("/api/admin/companies", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("adminToken");
          router.push(`/${locale}/admin`);
          return;
        }
        throw new Error(t("messages.failedToFetchCompanies"));
      }

      const data = await response.json();
      setCompanies(data.companies);
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const filterCompanies = () => {
    let filtered = companies;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (company) =>
          company.responsible
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          company.workName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          company.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          company.whatsapp.includes(searchTerm) ||
          company.workField.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter === "scanned") {
      filtered = filtered.filter((company) => company.isScanned);
    } else if (statusFilter === "not-scanned") {
      filtered = filtered.filter((company) => !company.isScanned);
    }

    setFilteredCompanies(filtered);
  };

  const exportToCSV = () => {
    const headers = [
      t("company.form.workName"),
      t("company.form.responsible"),
      t("messages.position"),
      t("admin.tables.email"),
      t("messages.whatsapp"),
      t("messages.workField"),
      t("messages.howKnew"),
      t("messages.status"),
      t("messages.registrationDate"),
      t("messages.scannedDate"),
    ];
    const csvContent = [
      headers.join(","),
      ...filteredCompanies.map((company) =>
        [
          company.workName,
          company.responsible,
          company.position,
          company.email,
          company.whatsapp,
          company.workField,
          company.howKnew,
          company.isScanned
            ? t("messages.scannedStatus")
            : t("messages.notScanned"),
          new Date(company.createdAt).toLocaleDateString(),
          company.scannedAt
            ? new Date(company.scannedAt).toLocaleDateString()
            : "",
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `companies-${new Date().toISOString().split("T")[0]}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
        <Navigation />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t("common.loading")}</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      <Navigation />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t("admin.tables.companies")}
            </h1>
            <p className="text-gray-600 mt-2">
              {t("messages.totalCompanies")}: {companies.length} |{" "}
              {t("messages.scanned")}:{" "}
              {companies.filter((c) => c.isScanned).length}
            </p>
          </div>
          <Button onClick={exportToCSV} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {t("messages.exportCSV")}
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              {t("messages.filterData")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  {t("messages.search")}
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder={t("messages.searchCompaniesPlaceholder")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">
                  {t("messages.status")}
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t("messages.all")}</SelectItem>
                    <SelectItem value="scanned">
                      {t("messages.scannedStatus")}
                    </SelectItem>
                    <SelectItem value="not-scanned">
                      {t("messages.notScanned")}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Table */}
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("company.form.workName")}</TableHead>
                    <TableHead>{t("company.form.responsible")}</TableHead>
                    <TableHead>{t("company.form.position")}</TableHead>
                    <TableHead>{t("admin.tables.email")}</TableHead>
                    <TableHead>{t("company.form.workField")}</TableHead>
                    <TableHead>{t("admin.tables.status")}</TableHead>
                    <TableHead>{t("admin.tables.joinedAt")}</TableHead>
                    <TableHead>{t("admin.tables.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCompanies.map((company) => (
                    <TableRow key={company._id}>
                      <TableCell className="font-medium">
                        {company.workName}
                      </TableCell>
                      <TableCell>{company.responsible}</TableCell>
                      <TableCell>{company.position}</TableCell>
                      <TableCell>{company.email}</TableCell>
                      <TableCell>{company.workField}</TableCell>
                      <TableCell>
                        <Badge
                          variant={company.isScanned ? "default" : "secondary"}
                        >
                          {company.isScanned ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-1" />
                          )}
                          {company.isScanned
                            ? t("messages.scannedStatus")
                            : t("messages.notScanned")}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {company.scannedAt
                          ? new Date(company.scannedAt).toLocaleDateString(
                              locale === "ar" ? "ar-SA" : "en-US"
                            )
                          : "-"}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Link href={`/${locale}/company/card/${company._id}`}>
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredCompanies.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {t("messages.noResultsFound")}
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
