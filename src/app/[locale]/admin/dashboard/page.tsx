"use client";

import { useState, useEffect } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Navigation from "@/components/Navigation";
import {
  Users,
  Building2,
  QrCode,
  UserCheck,
  Building,
  LogOut,
  BarChart3,
  ScanLine,
  Eye,
} from "lucide-react";
import Link from "next/link";

interface DashboardStats {
  totalGuests: number;
  totalCompanies: number;
  scannedGuests: number;
  scannedCompanies: number;
  totalScanned: number;
}

export default function AdminDashboard() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();

  const [stats, setStats] = useState<DashboardStats>({
    totalGuests: 0,
    totalCompanies: 0,
    scannedGuests: 0,
    scannedCompanies: 0,
    totalScanned: 0,
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    // Check authentication
    const token = localStorage.getItem("adminToken");
    if (!token) {
      router.push(`/${locale}/admin`);
      return;
    }

    fetchDashboardStats();
  }, [locale, router]);

  const fetchDashboardStats = async () => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch("/api/admin/stats", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("adminToken");
          router.push(`/${locale}/admin`);
          return;
        }
        throw new Error("Failed to fetch stats");
      }

      const data = await response.json();
      setStats(data.stats);
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("adminToken");
    router.push(`/${locale}/admin`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
        <Navigation />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t("common.loading")}</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      <Navigation />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t("admin.title")}
            </h1>
            <p className="text-gray-600 mt-2">
              {t("messages.welcomeToDashboard")}
            </p>
          </div>
          <Button onClick={handleLogout} variant="outline">
            <LogOut className="h-4 w-4 mr-2" />
            {t("navigation.logout")}
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.dashboard.stats.totalGuests")}
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalGuests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.dashboard.stats.totalCompanies")}
              </CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCompanies}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.dashboard.stats.scannedCodes")}
              </CardTitle>
              <QrCode className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalScanned}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.dashboard.stats.joinedGuests")}
              </CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.scannedGuests}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.dashboard.stats.joinedCompanies")}
              </CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.scannedCompanies}</div>
            </CardContent>
          </Card>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center">
                <ScanLine className="h-5 w-5 mr-2" />
                {t("admin.dashboard.actions.scanQR")}
              </CardTitle>
              <CardDescription>
                {t("admin.dashboard.descriptions.scanQR")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={`/${locale}/admin/scanner`}>
                <Button className="w-full">
                  <QrCode className="h-4 w-4 mr-2" />
                  {t("admin.dashboard.actions.scanQR")}
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                {t("admin.dashboard.actions.viewGuests")}
              </CardTitle>
              <CardDescription>
                {t("admin.dashboard.descriptions.viewGuests")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={`/${locale}/admin/guests`}>
                <Button variant="secondary" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  {t("admin.dashboard.actions.viewGuests")}
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="h-5 w-5 mr-2" />
                {t("admin.dashboard.actions.viewCompanies")}
              </CardTitle>
              <CardDescription>
                {t("admin.dashboard.descriptions.viewCompanies")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={`/${locale}/admin/companies`}>
                <Button variant="secondary" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  {t("admin.dashboard.actions.viewCompanies")}
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
