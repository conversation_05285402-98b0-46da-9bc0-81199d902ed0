"use client";

import { useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function CompanyRegistration() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();

  const [formData, setFormData] = useState({
    responsible: "",
    workName: "",
    position: "",
    whatsapp: "",
    workField: "",
    email: "",
    howKnew: "",
    comment: "",
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const howKnewOptions = [
    "facebook",
    "email",
    "twitter",
    "whatsapp",
    "instagram",
    "search",
    "radio",
    "other",
  ];

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      howKnew: value,
    }));
  };

  const validateForm = () => {
    const requiredFields = [
      "responsible",
      "workName",
      "position",
      "whatsapp",
      "workField",
      "email",
      "howKnew",
    ];

    for (const field of requiredFields) {
      if (!formData[field as keyof typeof formData].trim()) {
        setError(t("common.required"));
        return false;
      }
    }

    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setError(t("common.invalidEmail"));
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch("/api/company/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          locale,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Registration failed");
      }

      setSuccess(true);
      // Redirect to card page after 2 seconds
      setTimeout(() => {
        router.push(`/${locale}/company/card/${data.companyId}`);
      }, 2000);
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div
        className="min-h-screen"
        style={{
          background: "linear-gradient(135deg, #3C2F6A 0%, #**********%)",
        }}
      >
        <div className="flex justify-center pt-8 pb-4">
          <img
            src="/logo.jpeg"
            alt="Logo"
            className="w-[250px] h-[250px] object-contain rounded-3xl"
          />
        </div>
        <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-green-600 text-6xl mb-4">✓</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {t("company.success")}
              </h2>
              <p className="text-gray-600">
                {t("messages.redirectingCompany")}
              </p>
            </CardContent>
          </Card>
        </main>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen"
      style={{
        background: "linear-gradient(135deg, #3C2F6A 0%, #**********%)",
      }}
    >
      <div className="flex justify-center pt-8 pb-4">
        <img
          src="/logo.jpeg"
          alt="Logo"
          className="w-[250px] h-[250px] object-contain rounded-3xl"
        />
      </div>

      <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              {t("company.title")}
            </CardTitle>
            <CardDescription className="text-center">
              {t("company.subtitle")}
            </CardDescription>
          </CardHeader>

          <CardContent className="form-container">
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="responsible">
                    {t("company.form.responsible")}
                  </Label>
                  <Input
                    id="responsible"
                    name="responsible"
                    type="text"
                    value={formData.responsible}
                    onChange={handleInputChange}
                    required
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="workName">{t("company.form.workName")}</Label>
                  <Input
                    id="workName"
                    name="workName"
                    type="text"
                    value={formData.workName}
                    onChange={handleInputChange}
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="position">{t("company.form.position")}</Label>
                  <Input
                    id="position"
                    name="position"
                    type="text"
                    value={formData.position}
                    onChange={handleInputChange}
                    required
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="whatsapp">{t("company.form.whatsapp")}</Label>
                  <Input
                    id="whatsapp"
                    name="whatsapp"
                    type="tel"
                    value={formData.whatsapp}
                    onChange={handleInputChange}
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="workField">{t("company.form.workField")}</Label>
                <Input
                  id="workField"
                  name="workField"
                  type="text"
                  value={formData.workField}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">{t("company.form.email")}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="howKnew">{t("company.form.howKnew")}</Label>
                <Select onValueChange={handleSelectChange} disabled={loading}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("company.form.howKnew")} />
                  </SelectTrigger>
                  <SelectContent>
                    {howKnewOptions.map((option) => (
                      <SelectItem key={option} value={option}>
                        {t(`company.form.howKnewOptions.${option}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="comment">{t("company.form.comment")}</Label>
                <Textarea
                  id="comment"
                  name="comment"
                  value={formData.comment}
                  onChange={handleInputChange}
                  placeholder={t("company.form.commentPlaceholder")}
                  disabled={loading}
                  rows={4}
                />
              </div>

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("common.loading")}
                  </>
                ) : (
                  t("common.submit")
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
