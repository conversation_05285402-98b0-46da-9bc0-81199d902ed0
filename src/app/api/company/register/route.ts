import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/mongodb";
import Company from "@/models/Company";
import { generateQRCode, generateUniqueQRId } from "@/lib/qrcode";
import { sendCompanyConfirmationEmail } from "@/lib/email";

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const body = await request.json();
    const {
      responsible,
      workName,
      position,
      whatsapp,
      workField,
      email,
      howKnew,
      comment,
      locale,
    } = body;

    // Validate required fields
    if (
      !responsible ||
      !workName ||
      !position ||
      !whatsapp ||
      !workField ||
      !email ||
      !howKnew
    ) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingCompany = await Company.findOne({
      email: email.toLowerCase(),
    });
    if (existingCompany) {
      return NextResponse.json(
        {
          error:
            locale === "ar"
              ? "هذا البريد الإلكتروني مسجل بالفعل"
              : "This email is already registered",
        },
        { status: 409 }
      );
    }

    // Generate unique QR code ID
    const qrCodeId = generateUniqueQRId();

    // Create company data for QR code
    const companyData = {
      id: qrCodeId,
      responsible,
      workName,
      position,
      whatsapp,
      workField,
      email: email.toLowerCase(),
      howKnew,
      comment: comment || "",
      type: "company",
    };

    // Generate QR code
    const qrCodeDataURL = await generateQRCode(companyData, "company");

    // Create new company
    const company = new Company({
      responsible,
      workName,
      position,
      whatsapp,
      workField,
      email: email.toLowerCase(),
      howKnew,
      comment: comment || "",
      qrCode: qrCodeId,
    });

    await company.save();

    // Send confirmation email
    try {
      await sendCompanyConfirmationEmail(
        email,
        responsible,
        workName,
        qrCodeDataURL,
        locale
      );
    } catch (emailError) {
      console.error("Failed to send confirmation email:", emailError);
      // Don't fail the registration if email fails
    }

    return NextResponse.json({
      success: true,
      message:
        locale === "ar"
          ? "تم تسجيل الشركة بنجاح"
          : "Company registration successful",
      companyId: company._id.toString(),
    });
  } catch (error) {
    console.error("Company registration error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
