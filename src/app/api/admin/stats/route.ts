import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Guest from '@/models/Guest';
import Company from '@/models/Company';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Get statistics
    const [
      totalGuests,
      totalCompanies,
      scannedGuests,
      scannedCompanies
    ] = await Promise.all([
      Guest.countDocuments(),
      Company.countDocuments(),
      Guest.countDocuments({ isScanned: true }),
      Company.countDocuments({ isScanned: true })
    ]);

    const stats = {
      totalGuests,
      totalCompanies,
      scannedGuests,
      scannedCompanies,
      totalScanned: scannedGuests + scannedCompanies
    };

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
