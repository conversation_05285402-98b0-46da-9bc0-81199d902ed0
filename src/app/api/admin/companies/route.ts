import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/mongodb";
import Company from "@/models/Company";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function GET(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const token = authHeader.substring(7);

    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 });
    }

    await dbConnect();

    // Fetch all companies with sorting (most recent first)
    const companies = await Company.find({})
      .sort({ createdAt: -1 })
      .select("-__v");

    return NextResponse.json({
      success: true,
      companies: companies.map((company) => ({
        _id: company._id.toString(),
        responsible: company.responsible,
        workName: company.workName,
        position: company.position,
        whatsapp: company.whatsapp,
        workField: company.workField,
        email: company.email,
        howKnew: company.howKnew,
        comment: company.comment || "",
        isScanned: company.isScanned,
        scannedAt: company.scannedAt,
        createdAt: company.createdAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching companies:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
