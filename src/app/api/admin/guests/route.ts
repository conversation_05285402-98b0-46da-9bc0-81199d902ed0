import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Guest from '@/models/Guest';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Fetch all guests with sorting (most recent first)
    const guests = await Guest.find({})
      .sort({ createdAt: -1 })
      .select('-__v');

    return NextResponse.json({
      success: true,
      guests: guests.map(guest => ({
        _id: guest._id.toString(),
        name: guest.name,
        email: guest.email,
        mobile: guest.mobile,
        work: guest.work,
        isScanned: guest.isScanned,
        scannedAt: guest.scannedAt,
        createdAt: guest.createdAt
      }))
    });

  } catch (error) {
    console.error('Error fetching guests:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
