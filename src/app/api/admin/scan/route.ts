import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Guest from '@/models/Guest';
import Company from '@/models/Company';
import { validateQRCode } from '@/lib/qrcode';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Verify admin token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    await dbConnect();

    const body = await request.json();
    const { qrData } = body;

    if (!qrData) {
      return NextResponse.json({
        success: false,
        message: 'No QR data provided'
      });
    }

    // Validate QR code format
    const validation = validateQRCode(qrData);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        message: 'Invalid QR code format'
      });
    }

    const { type, data } = validation;

    // Find the record in database
    let record;
    let Model;
    
    if (type === 'guest') {
      Model = Guest;
      record = await Guest.findOne({ qrCode: data.id });
    } else if (type === 'company') {
      Model = Company;
      record = await Company.findOne({ qrCode: data.id });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Unknown QR code type'
      });
    }

    if (!record) {
      return NextResponse.json({
        success: false,
        message: 'QR code not found in database'
      });
    }

    // Check if already scanned
    const alreadyScanned = record.isScanned;
    
    // Update scan status
    if (!record.isScanned) {
      record.isScanned = true;
      record.scannedAt = new Date();
      await record.save();
    }

    return NextResponse.json({
      success: true,
      type,
      data: {
        id: record._id,
        name: record.name || record.responsible,
        email: record.email,
        mobile: record.mobile || record.whatsapp,
        work: record.work || record.workField,
        scannedAt: record.scannedAt
      },
      alreadyScanned,
      message: alreadyScanned 
        ? 'QR code already scanned previously'
        : 'QR code scanned successfully'
    });

  } catch (error) {
    console.error('Error scanning QR code:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    });
  }
}
