import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Guest from '@/models/Guest';
import { generateQRCode, generateUniqueQRId } from '@/lib/qrcode';
import { sendGuestConfirmationEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const body = await request.json();
    const { name, email, mobile, work, locale } = body;

    // Validate required fields
    if (!name || !email || !mobile || !work) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingGuest = await Guest.findOne({ email: email.toLowerCase() });
    if (existingGuest) {
      return NextResponse.json(
        { error: locale === 'ar' ? 'هذا البريد الإلكتروني مسجل بالفعل' : 'This email is already registered' },
        { status: 409 }
      );
    }

    // Generate unique QR code ID
    const qrCodeId = generateUniqueQRId();

    // Create guest data for QR code
    const guestData = {
      id: qrCodeId,
      name,
      email: email.toLowerCase(),
      mobile,
      work,
      type: 'guest'
    };

    // Generate QR code
    const qrCodeDataURL = await generateQRCode(guestData, 'guest');

    // Create new guest
    const guest = new Guest({
      name,
      email: email.toLowerCase(),
      mobile,
      work,
      qrCode: qrCodeId
    });

    await guest.save();

    // Send confirmation email
    try {
      await sendGuestConfirmationEmail(email, name, qrCodeDataURL, locale);
    } catch (emailError) {
      console.error('Failed to send confirmation email:', emailError);
      // Don't fail the registration if email fails
    }

    return NextResponse.json({
      success: true,
      message: locale === 'ar' ? 'تم التسجيل بنجاح' : 'Registration successful',
      guestId: guest._id.toString()
    });

  } catch (error) {
    console.error('Guest registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
