import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/mongodb";
import Guest from "@/models/Guest";
import { generateQRCode } from "@/lib/qrcode";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;
    const guest = await Guest.findById(id);

    if (!guest) {
      return NextResponse.json({ error: "Guest not found" }, { status: 404 });
    }

    // Generate QR code data URL for display
    const guestData = {
      id: guest.qrCode,
      name: guest.name,
      email: guest.email,
      mobile: guest.mobile,
      work: guest.work,
      type: "guest",
    };

    const qrCodeDataURL = await generateQRCode(guestData, "guest");

    return NextResponse.json({
      guest: {
        _id: guest._id.toString(),
        name: guest.name,
        email: guest.email,
        mobile: guest.mobile,
        work: guest.work,
        qrCode: guest.qrCode,
        createdAt: guest.createdAt,
      },
      qrCodeDataURL,
    });
  } catch (error) {
    console.error("Error fetching guest:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
